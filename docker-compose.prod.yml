version: '3.8'

services:
  # Backend service
  backend:
    image: ghcr.io/hieupham2010/vibe-coding/backend:latest
    container_name: products-backend-prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      - backend_data:/app/data
    networks:
      - products-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Frontend service
  frontend:
    image: ghcr.io/hieupham2010/vibe-coding/frontend:latest
    container_name: products-frontend-prod
    ports:
      - "8080:8080"
    depends_on:
      - backend
    networks:
      - products-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: products-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    networks:
      - products-network
    restart: unless-stopped

networks:
  products-network:
    driver: bridge

volumes:
  backend_data:
    driver: local
