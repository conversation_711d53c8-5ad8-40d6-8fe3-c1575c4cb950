services:
    # MSSQL Database service
    database:
        image: mcr.microsoft.com/mssql/server:2022-latest
        platform: linux/amd64
        container_name: products-database
        environment:
            - ACCEPT_EULA=Y
            - SA_PASSWORD=YourStrong@Passw0rd
            - MSSQL_PID=Express
        ports:
            - "1434:1433"
        volumes:
            - mssql_data:/var/opt/mssql
        networks:
            - products-network
        restart: unless-stopped

    # Backend service
    backend:
        build:
            context: ./nestjs-products-api
            dockerfile: Dockerfile
        container_name: products-backend
        ports:
            - "3000:3000"
        environment:
            - NODE_ENV=production
            - PORT=3000
            - DB_HOST=database
            - DB_PORT=1433
            - DB_USERNAME=sa
            - DB_PASSWORD=YourStrong@Passw0rd
            - DB_DATABASE=ProductsDB
        depends_on:
            - database
        networks:
            - products-network
        restart: unless-stopped
        healthcheck:
            test: ["CMD", "curl", "-f", "http://localhost:3000/"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 40s

    # Frontend service
    frontend:
        build:
            context: ./products-frontend
            dockerfile: Dockerfile
        container_name: products-frontend
        ports:
            - "8080:8080"
        depends_on:
            - backend
        networks:
            - products-network
        restart: unless-stopped
        healthcheck:
            test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 40s

networks:
    products-network:
        driver: bridge

volumes:
    mssql_data:
        driver: local
