# Docker Setup and CI/CD

This document explains how to run the application using Docker and the CI/CD pipeline setup.

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- GitHub account with repository access
- Node.js 18+ (for local development)

## Local Development with Docker

### 1. Build and Run with Docker Compose

```bash
# Build and start all services
docker-compose up --build

# Run in detached mode
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 2. Individual Service Commands

```bash
# Build backend only
docker build -t products-backend ./nestjs-products-api

# Build frontend only
docker build -t products-frontend ./products-frontend

# Run backend container
docker run -p 3000:3000 products-backend

# Run frontend container
docker run -p 8080:8080 products-frontend
```

## Production Deployment

### 1. Using Production Docker Compose

```bash
# Pull latest images and start
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# Check status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

### 2. Manual Docker Commands

```bash
# Pull images from GitHub Container Registry
docker pull ghcr.io/hieupham2010/vibe-coding/backend:latest
docker pull ghcr.io/hieupham2010/vibe-coding/frontend:latest

# Run containers
docker run -d -p 3000:3000 --name backend ghcr.io/hieupham2010/vibe-coding/backend:latest
docker run -d -p 8080:8080 --name frontend ghcr.io/hieupham2010/vibe-coding/frontend:latest
```

## CI/CD Pipeline

The GitHub Actions workflow automatically:

1. **On Pull Request:**
   - Runs tests for both backend and frontend
   - Performs linting and code quality checks
   - Builds applications to ensure they compile

2. **On Push to Main/Develop:**
   - Runs all tests and checks
   - Builds Docker images
   - Pushes images to GitHub Container Registry
   - Tags images with branch name and commit SHA

3. **On Push to Main:**
   - Deploys to staging environment (placeholder)

### Workflow Triggers

- `push` to `main` or `develop` branches
- `pull_request` to `main` branch

### Environment Variables

The pipeline uses these environment variables:
- `GITHUB_TOKEN` - Automatically provided by GitHub
- Custom secrets can be added in repository settings

## Container Registry

Images are stored in GitHub Container Registry:
- Backend: `ghcr.io/hieupham2010/vibe-coding/backend`
- Frontend: `ghcr.io/hieupham2010/vibe-coding/frontend`

## Health Checks

Both containers include health checks:
- Backend: `GET http://localhost:3000/`
- Frontend: `GET http://localhost:8080/health`

## Resource Limits

Production containers have resource limits:
- Backend: 512MB RAM, 0.5 CPU
- Frontend: 256MB RAM, 0.25 CPU

## Security Features

- Non-root users in containers
- Security headers in Nginx
- Minimal base images (Alpine Linux)
- Multi-stage builds to reduce image size

## Monitoring

Check container health:
```bash
# Check container status
docker ps

# Check health status
docker inspect --format='{{.State.Health.Status}}' container_name

# View container logs
docker logs container_name
```

## Troubleshooting

### Common Issues

1. **Port conflicts:**
   ```bash
   # Check what's using the port
   lsof -i :3000
   lsof -i :8080
   ```

2. **Container won't start:**
   ```bash
   # Check logs
   docker logs container_name
   
   # Check container configuration
   docker inspect container_name
   ```

3. **Build failures:**
   ```bash
   # Clean Docker cache
   docker system prune -a
   
   # Rebuild without cache
   docker-compose build --no-cache
   ```

### Useful Commands

```bash
# Remove all containers and images
docker-compose down --rmi all --volumes

# Clean up Docker system
docker system prune -a

# View resource usage
docker stats

# Execute commands in running container
docker exec -it container_name sh
```

## Environment Configuration

### Backend Environment Variables

Copy `.env.example` to `.env` and configure:
```bash
cd nestjs-products-api
cp .env.example .env
```

### Frontend Environment Variables

Copy `.env.example` to `.env` and configure:
```bash
cd products-frontend
cp .env.example .env
```

## Next Steps

1. Set up actual deployment target (AWS, GCP, Azure, etc.)
2. Configure domain and SSL certificates
3. Set up monitoring and logging
4. Configure backup strategies for data
5. Set up staging environment
