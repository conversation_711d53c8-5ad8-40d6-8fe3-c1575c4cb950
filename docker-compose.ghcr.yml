services:
    # MSSQL Database service
    database:
        image: mcr.microsoft.com/mssql/server:2022-latest
        container_name: products-database
        environment:
            - ACCEPT_EULA=Y
            - SA_PASSWORD=YourStrong@Passw0rd
            - MSSQL_PID=Express
        ports:
            - "1434:1433"
        volumes:
            - mssql_data:/var/opt/mssql
        networks:
            - products-network
        healthcheck:
            test: ["CMD-SHELL", "/opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q 'SELECT 1'"]
            interval: 30s
            timeout: 10s
            retries: 5
            start_period: 30s

    # Backend service using GHCR image
    backend:
        image: ghcr.io/hieupham2010/vibe-coding/backend:latest
        container_name: products-backend
        environment:
            - NODE_ENV=production
            - PORT=3000
            - DB_HOST=database
            - DB_PORT=1433
            - DB_USERNAME=sa
            - DB_PASSWORD=YourStrong@Passw0rd
            - DB_DATABASE=ProductsDB
        ports:
            - "3000:3000"
        depends_on:
            database:
                condition: service_healthy
        networks:
            - products-network
        restart: unless-stopped

    # Frontend service using GHCR image
    frontend:
        image: ghcr.io/hieupham2010/vibe-coding/frontend:latest
        container_name: products-frontend
        ports:
            - "8080:80"
        depends_on:
            - backend
        networks:
            - products-network
        restart: unless-stopped

volumes:
    mssql_data:
        driver: local

networks:
    products-network:
        driver: bridge
