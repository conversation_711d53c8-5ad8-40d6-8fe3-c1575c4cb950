# NestJS Products API

A basic CRUD API for managing products built with NestJS, TypeORM, and SQLite.

## Features

- ✅ Create, Read, Update, Delete (CRUD) operations for products
- ✅ SQLite database with TypeORM
- ✅ Input validation with class-validator
- ✅ Error handling
- ✅ Unit tests
- ✅ CORS enabled

## Product Schema

```typescript
{
  id: number; // Auto-generated primary key
  name: string; // Product name (required, max 255 chars)
  description: string; // Product description (optional)
  price: number; // Product price (required, min 0, 2 decimal places)
  stock: number; // Stock quantity (optional, default 0)
  category: string; // Product category (optional, max 100 chars)
  isActive: boolean; // Active status (optional, default true)
  createdAt: Date; // Creation timestamp
  updatedAt: Date; // Last update timestamp
}
```

## API Endpoints

### Create Product

```bash
POST /products
Content-Type: application/json

{
  "name": "iPhone 15",
  "description": "Latest iPhone model",
  "price": 999.99,
  "stock": 50,
  "category": "Electronics"
}
```

### Get All Products

```bash
GET /products
```

### Get Products by Category

```bash
GET /products?category=Electronics
```

### Get Active Products Only

```bash
GET /products/active
```

### Get Product by ID

```bash
GET /products/1
```

### Update Product

```bash
PATCH /products/1
Content-Type: application/json

{
  "price": 899.99,
  "stock": 75
}
```

### Delete Product

```bash
DELETE /products/1
```

## Installation & Setup

1. Install dependencies:

```bash
npm install
```

2. Start the development server:

```bash
npm run start:dev
```

The API will be available at `http://localhost:3000`

## Testing

Run unit tests:

```bash
npm test
```

Run tests in watch mode:

```bash
npm run test:watch
```

Run test coverage:

```bash
npm run test:cov
```

## Example Usage

### Create a product:

```bash
curl -X POST http://localhost:3000/products \
  -H "Content-Type: application/json" \
  -d '{"name": "MacBook Pro", "price": 2499.99, "category": "Electronics"}'
```

### Get all products:

```bash
curl http://localhost:3000/products
```

### Update a product:

```bash
curl -X PATCH http://localhost:3000/products/1 \
  -H "Content-Type: application/json" \
  -d '{"price": 2299.99}'
```

### Delete a product:

```bash
curl -X DELETE http://localhost:3000/products/1
```

## Database

The application uses SQLite with a file named `products.db` that will be created automatically when you start the application. The database schema is managed by TypeORM with automatic synchronization enabled (for development only).

## Project Structure

```
src/
├── products/
│   ├── dto/
│   │   ├── create-product.dto.ts
│   │   └── update-product.dto.ts
│   ├── entities/
│   │   └── product.entity.ts
│   ├── products.controller.ts
│   ├── products.service.ts
│   ├── products.module.ts
│   └── *.spec.ts (test files)
├── app.module.ts
└── main.ts
```

## License

This project is [MIT licensed](LICENSE).
