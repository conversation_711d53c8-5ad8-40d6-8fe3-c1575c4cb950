# Vibe Coding - Products Management System

A full-stack application for managing products with NestJS backend and React frontend, using MSSQL Server database.

## 🚀 Features

- **Backend**: NestJS with TypeORM and MSSQL Server
- **Frontend**: React with TypeScript and Vite
- **Database**: Microsoft SQL Server 2022
- **Containerization**: Docker and Docker Compose
- **CI/CD**: GitHub Actions with automated testing and Docker image building

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend│    │  NestJS Backend │    │  MSSQL Database │
│   (Port 8080)   │◄──►│   (Port 3000)   │◄──►│   (Port 1434)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Tech Stack

### Backend
- **NestJS** - Progressive Node.js framework
- **TypeORM** - Object-Relational Mapping
- **MSSQL** - Microsoft SQL Server driver
- **Class Validator** - Validation decorators
- **Docker** - Containerization

### Frontend
- **React 18** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool
- **CSS3** - Styling

### Database
- **Microsoft SQL Server 2022** - Relational database
- **Docker** - Containerized database

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local development)

### Running with Docker

1. Clone the repository:
```bash
git clone https://github.com/hieupham2010/vibe-coding.git
cd vibe-coding
```

2. Start all services:
```bash
docker-compose up -d
```

3. Create the database:
```bash
docker exec -it products-database /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P 'YourStrong@Passw0rd' -C -Q "CREATE DATABASE ProductsDB;"
```

4. Access the applications:
- Frontend: http://localhost:8080
- Backend API: http://localhost:3000
- Database: localhost:1434

## 📊 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/products` | Get all products |
| GET | `/products/active` | Get active products |
| GET | `/products/:id` | Get product by ID |
| POST | `/products` | Create new product |
| PATCH | `/products/:id` | Update product |
| DELETE | `/products/:id` | Delete product |

## 🧪 Testing

### Backend Tests
```bash
cd nestjs-products-api
npm test
```

### Frontend Tests
```bash
cd products-frontend
npm test
```

## 🔄 CI/CD Pipeline

The project uses GitHub Actions for continuous integration and deployment:

- **Test Stage**: Runs unit tests for both frontend and backend
- **Build Stage**: Builds Docker images and pushes to GitHub Container Registry
- **Deploy Stage**: Placeholder for deployment (can be extended)

## 📝 Environment Variables

### Backend (.env)
```
DB_HOST=database
DB_PORT=1433
DB_USERNAME=sa
DB_PASSWORD=YourStrong@Passw0rd
DB_DATABASE=ProductsDB
NODE_ENV=production
PORT=3000
```

## 🐳 Docker Images

The CI/CD pipeline builds and pushes Docker images to GitHub Container Registry:
- `ghcr.io/hieupham2010/vibe-coding/backend:latest`
- `ghcr.io/hieupham2010/vibe-coding/frontend:latest`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
