stages:
  - build
  - test
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  BACKEND_IMAGE: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA
  FRONTEND_IMAGE: $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA
  BACKEND_LATEST: $CI_REGISTRY_IMAGE/backend:latest
  FRONTEND_LATEST: $CI_REGISTRY_IMAGE/frontend:latest

services:
  - docker:dind

before_script:
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY

# Build Backend
build-backend:
  stage: build
  image: docker:latest
  script:
    - cd nestjs-products-api
    - docker build -t $BACKEND_IMAGE -t $BACKEND_LATEST .
    - docker push $BACKEND_IMAGE
    - docker push $BACKEND_LATEST
  only:
    - main
    - develop

# Build Frontend
build-frontend:
  stage: build
  image: docker:latest
  script:
    - cd products-frontend
    - docker build -t $FRONTEND_IMAGE -t $FRONTEND_LATEST .
    - docker push $FRONTEND_IMAGE
    - docker push $FRONTEND_LATEST
  only:
    - main
    - develop

# Test Backend
test-backend:
  stage: test
  image: node:18-alpine
  script:
    - cd nestjs-products-api
    - npm ci
    - npm run test
  only:
    - main
    - develop
    - merge_requests

# Test Frontend
test-frontend:
  stage: test
  image: node:18-alpine
  script:
    - cd products-frontend
    - npm ci
    - npm run test
  only:
    - main
    - develop
    - merge_requests

# Deploy to staging
deploy-staging:
  stage: deploy
  image: docker:latest
  script:
    - echo "Deploying to staging environment"
    - docker-compose -f docker-compose.yml up -d
  environment:
    name: staging
    url: http://staging.your-domain.com
  only:
    - develop

# Deploy to production
deploy-production:
  stage: deploy
  image: docker:latest
  script:
    - echo "Deploying to production environment"
    - docker-compose -f docker-compose.yml up -d
  environment:
    name: production
    url: http://your-domain.com
  only:
    - main
  when: manual
