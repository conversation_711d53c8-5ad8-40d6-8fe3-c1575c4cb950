import axios from "axios";
import type {
	Product,
	CreateProductDto,
	UpdateProductDto,
} from "../types/Product";

const API_BASE_URL =
	import.meta.env.VITE_API_BASE_URL || "http://localhost:3000";

const api = axios.create({
	baseURL: API_BASE_URL,
	headers: {
		"Content-Type": "application/json",
	},
});

export const productApi = {
	// Get all products
	getAll: async (category?: string): Promise<Product[]> => {
		const params = category ? { category } : {};
		const response = await api.get("/products", { params });
		return response.data;
	},

	// Get active products only
	getActive: async (): Promise<Product[]> => {
		const response = await api.get("/products/active");
		return response.data;
	},

	// Get product by ID
	getById: async (id: number): Promise<Product> => {
		const response = await api.get(`/products/${id}`);
		return response.data;
	},

	// Create new product
	create: async (product: CreateProductDto): Promise<Product> => {
		const response = await api.post("/products", product);
		return response.data;
	},

	// Update product
	update: async (id: number, product: UpdateProductDto): Promise<Product> => {
		const response = await api.patch(`/products/${id}`, product);
		return response.data;
	},

	// Delete product
	delete: async (id: number): Promise<{ message: string }> => {
		const response = await api.delete(`/products/${id}`);
		return response.data;
	},
};

export default api;
