// import { useState } from "react";
// import type {
// 	Product,
// 	CreateProductDto,
// 	UpdateProductDto,
// } from "./types/Product";
// import { productApi } from "./services/api";
// import ProductList from "./components/ProductList";
// import ProductForm from "./components/ProductForm";
import "./App.css";
import { Popover } from "radix-ui";

function App() {
	// const [showForm, setShowForm] = useState(false);
	// const [editingProduct, setEditingProduct] = useState<Product | null>(null);
	// const [isLoading, setIsLoading] = useState(false);
	// const [error, setError] = useState<string | null>(null);
	// const [refreshTrigger, setRefreshTrigger] = useState(0);

	// const handleCreateProduct = async (data: CreateProductDto) => {
	// 	try {
	// 		setIsLoading(true);
	// 		setError(null);
	// 		await productApi.create(data);
	// 		setShowForm(false);
	// 		setRefreshTrigger((prev) => prev + 1);
	// 	} catch (err) {
	// 		setError("Failed to create product");
	// 		console.error("Error creating product:", err);
	// 	} finally {
	// 		setIsLoading(false);
	// 	}
	// };

	// const handleUpdateProduct = async (data: UpdateProductDto) => {
	// 	if (!editingProduct) return;

	// 	try {
	// 		setIsLoading(true);
	// 		setError(null);
	// 		await productApi.update(editingProduct.id, data);
	// 		setEditingProduct(null);
	// 		setShowForm(false);
	// 		setRefreshTrigger((prev) => prev + 1);
	// 	} catch (err) {
	// 		setError("Failed to update product");
	// 		console.error("Error updating product:", err);
	// 	} finally {
	// 		setIsLoading(false);
	// 	}
	// };

	// const handleFormSubmit = async (
	// 	data: CreateProductDto | UpdateProductDto
	// ) => {
	// 	if (editingProduct) {
	// 		await handleUpdateProduct(data as UpdateProductDto);
	// 	} else {
	// 		await handleCreateProduct(data as CreateProductDto);
	// 	}
	// };

	// const handleDeleteProduct = async (id: number) => {
	// 	try {
	// 		setError(null);
	// 		await productApi.delete(id);
	// 		setRefreshTrigger((prev) => prev + 1);
	// 	} catch (err) {
	// 		setError("Failed to delete product");
	// 		console.error("Error deleting product:", err);
	// 		throw err;
	// 	}
	// };

	// const handleEditProduct = (product: Product) => {
	// 	setEditingProduct(product);
	// 	setShowForm(true);
	// };

	// const handleCloseForm = () => {
	// 	setShowForm(false);
	// 	setEditingProduct(null);
	// 	setError(null);
	// };

	// const handleNewProduct = () => {
	// 	setEditingProduct(null);
	// 	setShowForm(true);
	// };

	return (
		// <div className="app">
		// 	<header className="app-header">
		// 		<h1>Product Management System</h1>
		// 		<button className="btn btn-primary" onClick={handleNewProduct}>
		// 			Add New Product
		// 		</button>
		// 	</header>

		// 	{error && (
		// 		<div className="error-banner">
		// 			{error}
		// 			<button onClick={() => setError(null)}>×</button>
		// 		</div>
		// 	)}

		// 	<main className="app-main">
		// 		<ProductList
		// 			onEdit={handleEditProduct}
		// 			onDelete={handleDeleteProduct}
		// 			refreshTrigger={refreshTrigger}
		// 		/>
		// 	</main>

		// 	{showForm && (
		// 		<ProductForm
		// 			product={editingProduct}
		// 			onSubmit={handleFormSubmit}
		// 			onCancel={handleCloseForm}
		// 			isLoading={isLoading}
		// 		/>
		// 	)}
		// </div>

		<Popover.Root>
			<Popover.Trigger>More info</Popover.Trigger>
			<Popover.Portal>
				<Popover.Content>
					Some more info…
					<Popover.Arrow />
				</Popover.Content>
			</Popover.Portal>
		</Popover.Root>
	);
}

export default App;
