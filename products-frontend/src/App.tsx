import { useState, useEffect } from "react";
import {
	<PERSON><PERSON>,
	ProductList,
	ProductForm,
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	type Product,
	type CreateProductDto,
	type UpdateProductDto,
} from "../../vibe-ui-library/dist/index.js";
import { productApi } from "./services/api";
import "./App.css";

function App() {
	const [products, setProducts] = useState<Product[]>([]);
	const [showForm, setShowForm] = useState(false);
	const [editingProduct, setEditingProduct] = useState<Product | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [loading, setLoading] = useState(true);

	// Load products on component mount
	useEffect(() => {
		loadProducts();
	}, []);

	const loadProducts = async () => {
		try {
			setLoading(true);
			setError(null);
			const data = await productApi.getAll();
			setProducts(data);
		} catch (err) {
			setError("Failed to load products");
			console.error("Error loading products:", err);
		} finally {
			setLoading(false);
		}
	};

	const handleCreateProduct = async (data: CreateProductDto) => {
		try {
			setIsLoading(true);
			setError(null);
			await productApi.create(data);
			setShowForm(false);
			await loadProducts(); // Reload products
		} catch (err) {
			setError("Failed to create product");
			console.error("Error creating product:", err);
		} finally {
			setIsLoading(false);
		}
	};

	const handleUpdateProduct = async (data: UpdateProductDto) => {
		if (!editingProduct) return;

		try {
			setIsLoading(true);
			setError(null);
			await productApi.update(editingProduct.id, data);
			setEditingProduct(null);
			setShowForm(false);
			await loadProducts(); // Reload products
		} catch (err) {
			setError("Failed to update product");
			console.error("Error updating product:", err);
		} finally {
			setIsLoading(false);
		}
	};

	const handleFormSubmit = async (
		data: CreateProductDto | UpdateProductDto
	) => {
		if (editingProduct) {
			await handleUpdateProduct(data as UpdateProductDto);
		} else {
			await handleCreateProduct(data as CreateProductDto);
		}
	};

	const handleDeleteProduct = async (id: number) => {
		try {
			setError(null);
			await productApi.delete(id);
			await loadProducts(); // Reload products
		} catch (err) {
			setError("Failed to delete product");
			console.error("Error deleting product:", err);
			throw err;
		}
	};

	const handleEditProduct = (product: Product) => {
		setEditingProduct(product);
		setShowForm(true);
	};

	const handleCloseForm = () => {
		setShowForm(false);
		setEditingProduct(null);
		setError(null);
	};

	const handleNewProduct = () => {
		setEditingProduct(null);
		setShowForm(true);
	};

	return (
		<div className="app">
			<header className="app-header">
				<h1>Product Management System</h1>
				<Dialog open={showForm} onOpenChange={setShowForm}>
					<DialogTrigger asChild>
						<Button onClick={handleNewProduct}>
							Add New Product
						</Button>
					</DialogTrigger>
					<DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
						<DialogHeader>
							<DialogTitle>
								{editingProduct
									? "Edit Product"
									: "Create New Product"}
							</DialogTitle>
						</DialogHeader>
						<ProductForm
							product={editingProduct}
							onSubmit={handleFormSubmit}
							onCancel={handleCloseForm}
							isLoading={isLoading}
						/>
					</DialogContent>
				</Dialog>
			</header>

			{error && (
				<div className="error-banner">
					{error}
					<button onClick={() => setError(null)}>×</button>
				</div>
			)}

			<main className="app-main">
				<ProductList
					products={products}
					onEdit={handleEditProduct}
					onDelete={handleDeleteProduct}
					onRefresh={loadProducts}
					loading={loading}
					error={error}
				/>
			</main>
		</div>
	);
}

export default App;
