.product-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.product-card.inactive {
  opacity: 0.7;
  background: #f8f9fa;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.product-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 10px;
}

.product-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.product-description {
  color: #666;
  margin: 0 0 15px 0;
  line-height: 1.4;
}

.product-details {
  margin-bottom: 15px;
}

.price-stock {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.price {
  font-size: 20px;
  font-weight: 700;
  color: #2c5aa0;
}

.stock {
  font-size: 14px;
  color: #666;
  background: #f1f3f4;
  padding: 4px 8px;
  border-radius: 4px;
}

.category {
  margin-bottom: 10px;
}

.category-badge {
  background: #e3f2fd;
  color: #1565c0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.dates {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.dates small {
  color: #888;
  font-size: 11px;
}

.product-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  flex: 1;
}

.btn-edit {
  background: #007bff;
  color: white;
}

.btn-edit:hover {
  background: #0056b3;
}

.btn-delete {
  background: #dc3545;
  color: white;
}

.btn-delete:hover {
  background: #c82333;
}

@media (max-width: 480px) {
  .product-card {
    padding: 15px;
  }
  
  .product-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .price-stock {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
