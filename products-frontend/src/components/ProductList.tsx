import React, { useState, useEffect } from "react";
import type { Product } from "../types/Product";
import { productApi } from "../services/api";
import ProductCard from "./ProductCard";
import "./ProductList.css";

interface ProductListProps {
	onEdit: (product: Product) => void;
	onDelete: (id: number) => void;
	refreshTrigger: number;
}

const ProductList: React.FC<ProductListProps> = ({
	onEdit,
	onDelete,
	refreshTrigger,
}) => {
	const [products, setProducts] = useState<Product[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [categoryFilter, setCategoryFilter] = useState<string>("");
	const [showActiveOnly, setShowActiveOnly] = useState(false);

	const fetchProducts = async () => {
		try {
			setLoading(true);
			setError(null);

			let data: Product[];
			if (showActiveOnly) {
				data = await productApi.getActive();
			} else if (categoryFilter) {
				data = await productApi.getAll(categoryFilter);
			} else {
				data = await productApi.getAll();
			}

			setProducts(data);
		} catch (err) {
			setError("Failed to fetch products");
			console.error("Error fetching products:", err);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchProducts();
	}, [categoryFilter, showActiveOnly, refreshTrigger]);

	const handleDelete = async (id: number) => {
		if (window.confirm("Are you sure you want to delete this product?")) {
			try {
				await onDelete(id);
				fetchProducts(); // Refresh the list
			} catch (err) {
				setError("Failed to delete product");
			}
		}
	};

	const categories = [
		...new Set(products.map((p) => p.category).filter(Boolean)),
	];

	if (loading) {
		return <div className="loading">Loading products...</div>;
	}

	if (error) {
		return <div className="error">{error}</div>;
	}

	return (
		<div className="product-list">
			<div className="filters">
				<div className="filter-group">
					<label htmlFor="category-filter">Filter by Category:</label>
					<select
						id="category-filter"
						value={categoryFilter}
						onChange={(e) => setCategoryFilter(e.target.value)}
					>
						<option value="">All Categories</option>
						{categories.map((category) => (
							<option key={category} value={category}>
								{category}
							</option>
						))}
					</select>
				</div>

				<div className="filter-group">
					<label>
						<input
							type="checkbox"
							checked={showActiveOnly}
							onChange={(e) =>
								setShowActiveOnly(e.target.checked)
							}
						/>
						Show Active Only
					</label>
				</div>
			</div>

			{products.length === 0 ? (
				<div className="no-products">No products found</div>
			) : (
				<div className="products-grid">
					{products.map((product) => (
						<ProductCard
							key={product.id}
							product={product}
							onEdit={onEdit}
							onDelete={handleDelete}
						/>
					))}
				</div>
			)}
		</div>
	);
};

export default ProductList;
