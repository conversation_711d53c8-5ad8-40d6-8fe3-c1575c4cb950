import React from "react";
import type { Product } from "../types/Product";
import "./ProductCard.css";

interface ProductCardProps {
	product: Product;
	onEdit: (product: Product) => void;
	onDelete: (id: number) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({
	product,
	onEdit,
	onDelete,
}) => {
	const formatPrice = (price: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(price);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString();
	};

	return (
		<div className={`product-card ${!product.isActive ? "inactive" : ""}`}>
			<div className="product-header">
				<h3 className="product-name">{product.name}</h3>
				<div className="product-status">
					<span
						className={`status-badge ${
							product.isActive ? "active" : "inactive"
						}`}
					>
						{product.isActive ? "Active" : "Inactive"}
					</span>
				</div>
			</div>

			{product.description && (
				<p className="product-description">{product.description}</p>
			)}

			<div className="product-details">
				<div className="price-stock">
					<span className="price">{formatPrice(product.price)}</span>
					<span className="stock">Stock: {product.stock}</span>
				</div>

				{product.category && (
					<div className="category">
						<span className="category-badge">
							{product.category}
						</span>
					</div>
				)}

				<div className="dates">
					<small>Created: {formatDate(product.createdAt)}</small>
					{product.updatedAt !== product.createdAt && (
						<small>Updated: {formatDate(product.updatedAt)}</small>
					)}
				</div>
			</div>

			<div className="product-actions">
				<button
					className="btn btn-edit"
					onClick={() => onEdit(product)}
				>
					Edit
				</button>
				<button
					className="btn btn-delete"
					onClick={() => onDelete(product.id)}
				>
					Delete
				</button>
			</div>
		</div>
	);
};

export default ProductCard;
