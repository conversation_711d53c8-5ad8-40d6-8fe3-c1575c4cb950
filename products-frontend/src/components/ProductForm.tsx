import React, { useState, useEffect } from "react";
import type {
	Product,
	CreateProductDto,
	UpdateProductDto,
} from "../types/Product";
import "./ProductForm.css";

interface ProductFormProps {
	product?: Product | null;
	onSubmit: (data: CreateProductDto | UpdateProductDto) => void;
	onCancel: () => void;
	isLoading?: boolean;
}

const ProductForm: React.FC<ProductFormProps> = ({
	product,
	onSubmit,
	onCancel,
	isLoading = false,
}) => {
	const [formData, setFormData] = useState({
		name: "",
		description: "",
		price: "",
		stock: "",
		category: "",
		isActive: true,
	});

	const [errors, setErrors] = useState<Record<string, string>>({});

	useEffect(() => {
		if (product) {
			setFormData({
				name: product.name,
				description: product.description || "",
				price: product.price.toString(),
				stock: product.stock.toString(),
				category: product.category || "",
				isActive: product.isActive,
			});
		} else {
			setFormData({
				name: "",
				description: "",
				price: "",
				stock: "",
				category: "",
				isActive: true,
			});
		}
	}, [product]);

	const validateForm = () => {
		const newErrors: Record<string, string> = {};

		if (!formData.name.trim()) {
			newErrors.name = "Product name is required";
		}

		if (
			!formData.price ||
			isNaN(Number(formData.price)) ||
			Number(formData.price) < 0
		) {
			newErrors.price = "Valid price is required";
		}

		if (
			formData.stock &&
			(isNaN(Number(formData.stock)) || Number(formData.stock) < 0)
		) {
			newErrors.stock = "Stock must be a valid number";
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		if (!validateForm()) {
			return;
		}

		const submitData: CreateProductDto | UpdateProductDto = {
			name: formData.name.trim(),
			description: formData.description.trim() || undefined,
			price: Number(formData.price),
			stock: formData.stock ? Number(formData.stock) : undefined,
			category: formData.category.trim() || undefined,
			isActive: formData.isActive,
		};

		onSubmit(submitData);
	};

	const handleChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
	) => {
		const { name, value, type } = e.target;
		setFormData((prev) => ({
			...prev,
			[name]:
				type === "checkbox"
					? (e.target as HTMLInputElement).checked
					: value,
		}));

		// Clear error when user starts typing
		if (errors[name]) {
			setErrors((prev) => ({ ...prev, [name]: "" }));
		}
	};

	return (
		<div className="product-form-overlay">
			<div className="product-form-container">
				<h2>{product ? "Edit Product" : "Create New Product"}</h2>

				<form onSubmit={handleSubmit} className="product-form">
					<div className="form-group">
						<label htmlFor="name">Product Name *</label>
						<input
							type="text"
							id="name"
							name="name"
							value={formData.name}
							onChange={handleChange}
							className={errors.name ? "error" : ""}
							disabled={isLoading}
						/>
						{errors.name && (
							<span className="error-message">{errors.name}</span>
						)}
					</div>

					<div className="form-group">
						<label htmlFor="description">Description</label>
						<textarea
							id="description"
							name="description"
							value={formData.description}
							onChange={handleChange}
							rows={3}
							disabled={isLoading}
						/>
					</div>

					<div className="form-row">
						<div className="form-group">
							<label htmlFor="price">Price *</label>
							<input
								type="number"
								id="price"
								name="price"
								value={formData.price}
								onChange={handleChange}
								step="0.01"
								min="0"
								className={errors.price ? "error" : ""}
								disabled={isLoading}
							/>
							{errors.price && (
								<span className="error-message">
									{errors.price}
								</span>
							)}
						</div>

						<div className="form-group">
							<label htmlFor="stock">Stock</label>
							<input
								type="number"
								id="stock"
								name="stock"
								value={formData.stock}
								onChange={handleChange}
								min="0"
								className={errors.stock ? "error" : ""}
								disabled={isLoading}
							/>
							{errors.stock && (
								<span className="error-message">
									{errors.stock}
								</span>
							)}
						</div>
					</div>

					<div className="form-group">
						<label htmlFor="category">Category</label>
						<input
							type="text"
							id="category"
							name="category"
							value={formData.category}
							onChange={handleChange}
							disabled={isLoading}
						/>
					</div>

					<div className="form-group checkbox-group">
						<label>
							<input
								type="checkbox"
								name="isActive"
								checked={formData.isActive}
								onChange={handleChange}
								disabled={isLoading}
							/>
							Active Product
						</label>
					</div>

					<div className="form-actions">
						<button
							type="button"
							onClick={onCancel}
							className="btn btn-cancel"
							disabled={isLoading}
						>
							Cancel
						</button>
						<button
							type="submit"
							className="btn btn-primary"
							disabled={isLoading}
						>
							{isLoading
								? "Saving..."
								: product
								? "Update Product"
								: "Create Product"}
						</button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default ProductForm;
