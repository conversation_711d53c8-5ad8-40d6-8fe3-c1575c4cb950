/* Import UI Library Styles */
@import "../../vibe-ui-library/src/styles/globals.css";

* {
  box-sizing: border-box;
}

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #213547;
  background-color: #ffffff;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: #f8f9fa;
}

#root {
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
}

button {
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s;
}

button:focus,
button:focus-visible {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

input, textarea, select {
  font-family: inherit;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
