{"name": "@vibe/ui-library", "version": "1.0.0", "description": "A React component library built with Radix UI primitives", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "build:watch": "rollup -c -w", "dev": "rollup -c -w", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.1", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.1", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@storybook/test": "^8.4.7", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "eslint": "^9.25.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^0.11.1", "react": "^19.1.0", "react-dom": "^19.1.0", "rimraf": "^6.0.1", "rollup": "^4.28.1", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "storybook": "^8.4.7", "typescript": "^5.8.3"}, "keywords": ["react", "components", "ui", "radix-ui", "typescript", "library"], "author": "VibeCoding", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/hieupham2010/vibe-coding.git", "directory": "vibe-ui-library"}}