# Vibe UI Library

A React component library built with Radix UI primitives for the VibeCoding project.

## Features

- 🎨 Built with Radix UI primitives
- 📦 Bundled with Rollup
- 📚 Documented with Storybook
- 🔧 TypeScript support
- 🎯 Product-specific components
- ♿ Accessible by default

## Installation

```bash
npm install @vibe/ui-library
```

## Usage

```tsx
import { Button, ProductCard } from '@vibe/ui-library';

function App() {
  return (
    <div>
      <Button variant="primary">Click me</Button>
      <ProductCard product={product} onEdit={handleEdit} onDelete={handleDelete} />
    </div>
  );
}
```

## Development

```bash
# Install dependencies
npm install

# Start Storybook
npm run storybook

# Build library
npm run build

# Watch mode
npm run dev
```

## Components

### Base UI Components
- Button
- Input
- Card
- Dialog
- Label
- Select
- Switch
- Separator
- Toast
- Tooltip

### Product Components
- ProductCard
- ProductForm
- ProductList

## License

MIT
