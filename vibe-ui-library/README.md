# Vibe UI Library

A React component library built with Radix UI primitives for the VibeCoding project.

## Features

-   🎨 Built with Radix UI primitives
-   📦 Bundled with Rollup
-   📚 Documented with Storybook
-   🔧 TypeScript support
-   🎯 Product-specific components
-   ♿ Accessible by default

## Installation

### For Local Development

Since this is a local library, you can use it in two ways:

#### Method 1: Relative Import (Current)

```tsx
// Import from the built library
import { Button, ProductCard } from '../../vibe-ui-library/dist/index.js';

// Import styles in your main CSS file
@import "../../vibe-ui-library/src/styles/globals.css";
```

#### Method 2: NPM Link (Alternative)

```bash
# In the library directory
cd vibe-ui-library
npm link

# In your project directory
cd ../products-frontend
npm link @vibe/ui-library
```

#### Method 3: File Dependency (Package.json)

```json
{
	"dependencies": {
		"@vibe/ui-library": "file:../vibe-ui-library"
	}
}
```

### For Production

```bash
npm install @vibe/ui-library
```

## Usage

```tsx
import {
	Button,
	ProductCard,
	ProductList,
	ProductForm,
} from "@vibe/ui-library";
import "@vibe/ui-library/dist/index.css";

function App() {
	return (
		<div>
			<Button variant="primary">Click me</Button>
			<ProductCard
				product={product}
				onEdit={handleEdit}
				onDelete={handleDelete}
			/>
			<ProductList
				products={products}
				onEdit={handleEdit}
				onDelete={handleDelete}
				loading={loading}
				error={error}
			/>
		</div>
	);
}
```

## Development

```bash
# Install dependencies
npm install

# Start Storybook (http://localhost:6006)
npm run storybook

# Build library
npm run build

# Watch mode for development
npm run build:watch
```

## Components

### Base UI Components

-   **Button** - Multiple variants (default, destructive, outline, secondary, ghost, link)
-   **Input** - Form input with validation styling
-   **Card** - Flexible card component with header, content, footer
-   **Dialog** - Modal dialog with overlay and accessibility
-   **Label** - Form labels with Radix UI integration
-   **Select** - Dropdown select with search and keyboard navigation
-   **Switch** - Toggle switch component
-   **Separator** - Visual divider component
-   **Toast** - Notification system
-   **Tooltip** - Hover tooltips

### Product Components

-   **ProductCard** - Displays product information with edit/delete actions
-   **ProductForm** - Form for creating/editing products with validation
-   **ProductList** - List view with filtering, search, and pagination

## Project Structure

```
vibe-ui-library/
├── src/
│   ├── components/
│   │   ├── ui/           # Base UI components
│   │   └── product/      # Product-specific components
│   ├── types/            # TypeScript type definitions
│   ├── lib/              # Utility functions
│   └── styles/           # CSS styles
├── .storybook/           # Storybook configuration
├── dist/                 # Built library files
└── package.json          # Library configuration
```

## Integration Example

The library is currently integrated with the `products-frontend` project and demonstrates:

-   Product management interface
-   Form handling with validation
-   List filtering and search
-   Modal dialogs for editing
-   Responsive design
-   Accessibility features

## Storybook

View component documentation and examples at: http://localhost:6006

## License

MIT
