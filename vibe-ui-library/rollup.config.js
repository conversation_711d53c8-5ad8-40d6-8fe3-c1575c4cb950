import resolve from "@rollup/plugin-node-resolve";
import commonjs from "@rollup/plugin-commonjs";
import typescript from "@rollup/plugin-typescript";
import peerDepsExternal from "rollup-plugin-peer-deps-external";
import postcss from "rollup-plugin-postcss";
import dts from "rollup-plugin-dts";

const packageJson = require("./package.json");

export default [
	{
		input: "src/index.ts",
		output: [
			{
				file: packageJson.main,
				format: "cjs",
				sourcemap: true,
				name: "vibe-ui-library",
			},
			{
				file: packageJson.module,
				format: "esm",
				sourcemap: true,
			},
		],
		plugins: [
			peerDepsExternal(),
			resolve({
				browser: true,
				preferBuiltins: false,
			}),
			commonjs(),
			typescript({
				tsconfig: "./tsconfig.json",
				exclude: ["**/*.stories.*", "**/*.test.*"],
			}),
			postcss({
				extract: true,
				minimize: true,
			}),
		],
		external: [
			"react",
			"react-dom",
			"@radix-ui/react-dialog",
			"@radix-ui/react-label",
			"@radix-ui/react-popover",
			"@radix-ui/react-select",
			"@radix-ui/react-separator",
			"@radix-ui/react-slot",
			"@radix-ui/react-switch",
			"@radix-ui/react-toast",
			"@radix-ui/react-tooltip",
			"class-variance-authority",
			"clsx",
			"lucide-react",
		],
	},
	{
		input: "dist/index.d.ts",
		output: [{ file: "dist/index.d.ts", format: "esm" }],
		plugins: [dts()],
		external: [/\.css$/],
	},
];
