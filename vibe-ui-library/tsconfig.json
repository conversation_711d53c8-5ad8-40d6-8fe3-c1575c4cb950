{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "outDir": "dist", "rootDir": "src"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.stories.*", "**/*.test.*"]}