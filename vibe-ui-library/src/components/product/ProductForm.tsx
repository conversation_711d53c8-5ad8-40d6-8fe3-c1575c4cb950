import * as React from "react";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { Label } from "../ui/Label";
import { Switch } from "../ui/Switch";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "../ui/Card";
import { Separator } from "../ui/Separator";
import { cn } from "../../lib/utils";
import type { Product, CreateProductDto, UpdateProductDto } from "../../types/Product";

export interface ProductFormProps {
  product?: Product | null;
  onSubmit: (data: CreateProductDto | UpdateProductDto) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

const ProductForm = React.forwardRef<HTMLDivElement, ProductFormProps>(
  ({ product, onSubmit, onCancel, isLoading = false, className }, ref) => {
    const [formData, setFormData] = React.useState({
      name: "",
      description: "",
      price: "",
      stock: "",
      category: "",
      isActive: true,
    });

    const [errors, setErrors] = React.useState<Record<string, string>>({});

    React.useEffect(() => {
      if (product) {
        setFormData({
          name: product.name,
          description: product.description || "",
          price: product.price.toString(),
          stock: product.stock?.toString() || "",
          category: product.category || "",
          isActive: product.isActive,
        });
      } else {
        setFormData({
          name: "",
          description: "",
          price: "",
          stock: "",
          category: "",
          isActive: true,
        });
      }
      setErrors({});
    }, [product]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));
      
      // Clear error when user starts typing
      if (errors[name]) {
        setErrors(prev => ({ ...prev, [name]: "" }));
      }
    };

    const handleSwitchChange = (checked: boolean) => {
      setFormData(prev => ({ ...prev, isActive: checked }));
    };

    const validateForm = () => {
      const newErrors: Record<string, string> = {};

      if (!formData.name.trim()) {
        newErrors.name = "Product name is required";
      }

      if (!formData.price.trim()) {
        newErrors.price = "Price is required";
      } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
        newErrors.price = "Price must be a positive number";
      }

      if (formData.stock && (isNaN(Number(formData.stock)) || Number(formData.stock) < 0)) {
        newErrors.stock = "Stock must be a non-negative number";
      }

      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) {
        return;
      }

      const submitData: CreateProductDto | UpdateProductDto = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        price: Number(formData.price),
        stock: formData.stock ? Number(formData.stock) : undefined,
        category: formData.category.trim() || undefined,
        isActive: formData.isActive,
      };

      onSubmit(submitData);
    };

    return (
      <Card ref={ref} className={cn("w-full max-w-2xl", className)}>
        <CardHeader>
          <h2 className="text-xl font-semibold">
            {product ? "Edit Product" : "Create New Product"}
          </h2>
        </CardHeader>

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Product Name *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={errors.name ? "border-destructive" : ""}
                disabled={isLoading}
                placeholder="Enter product name"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                disabled={isLoading}
                placeholder="Enter product description"
                rows={3}
                className="flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="price">Price *</Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.price}
                  onChange={handleChange}
                  className={errors.price ? "border-destructive" : ""}
                  disabled={isLoading}
                  placeholder="0.00"
                />
                {errors.price && (
                  <p className="text-sm text-destructive">{errors.price}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="stock">Stock</Label>
                <Input
                  id="stock"
                  name="stock"
                  type="number"
                  min="0"
                  value={formData.stock}
                  onChange={handleChange}
                  className={errors.stock ? "border-destructive" : ""}
                  disabled={isLoading}
                  placeholder="0"
                />
                {errors.stock && (
                  <p className="text-sm text-destructive">{errors.stock}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Input
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                disabled={isLoading}
                placeholder="Enter product category"
              />
            </div>

            <Separator />

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={handleSwitchChange}
                disabled={isLoading}
              />
              <Label htmlFor="isActive">Active Product</Label>
            </div>
          </CardContent>

          <CardFooter className="flex gap-2">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? "Saving..." : product ? "Update Product" : "Create Product"}
            </Button>
          </CardFooter>
        </form>
      </Card>
    );
  }
);

ProductForm.displayName = "ProductForm";

export { ProductForm };
