import type { <PERSON>a, StoryObj } from '@storybook/react';
import { ProductList } from './ProductList';
import type { Product } from '../../types/Product';

const meta: Meta<typeof ProductList> = {
  title: 'Product/ProductList',
  component: ProductList,
  parameters: {
    layout: 'fullscreen',
    docs: {
      story: {
        height: '600px',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleProducts: Product[] = [
  {
    id: 1,
    name: 'Wireless Bluetooth Headphones',
    description: 'Premium noise-cancelling wireless headphones with 30-hour battery life.',
    price: 299.99,
    stock: 15,
    category: 'Electronics',
    isActive: true,
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-20T14:45:00Z',
  },
  {
    id: 2,
    name: 'Vintage Leather Wallet',
    description: 'Handcrafted genuine leather wallet with multiple card slots.',
    price: 89.99,
    stock: 0,
    category: 'Accessories',
    isActive: false,
    createdAt: '2024-01-10T09:15:00Z',
    updatedAt: '2024-01-10T09:15:00Z',
  },
  {
    id: 3,
    name: 'Organic Cotton T-Shirt',
    description: 'Comfortable and sustainable organic cotton t-shirt.',
    price: 29.99,
    stock: 50,
    category: 'Clothing',
    isActive: true,
    createdAt: '2024-01-25T16:20:00Z',
    updatedAt: '2024-01-25T16:20:00Z',
  },
  {
    id: 4,
    name: 'Smart Watch',
    description: 'Feature-rich smartwatch with health monitoring.',
    price: 199.99,
    stock: 8,
    category: 'Electronics',
    isActive: true,
    createdAt: '2024-01-18T11:45:00Z',
    updatedAt: '2024-01-22T09:30:00Z',
  },
  {
    id: 5,
    name: 'Ceramic Coffee Mug',
    description: 'Beautiful handmade ceramic coffee mug.',
    price: 24.99,
    stock: 25,
    category: 'Home & Kitchen',
    isActive: true,
    createdAt: '2024-01-12T14:20:00Z',
    updatedAt: '2024-01-12T14:20:00Z',
  },
  {
    id: 6,
    name: 'Yoga Mat',
    description: 'Non-slip yoga mat perfect for all types of yoga practice.',
    price: 49.99,
    stock: 12,
    category: 'Sports',
    isActive: true,
    createdAt: '2024-01-08T08:15:00Z',
    updatedAt: '2024-01-15T16:45:00Z',
  },
];

export const Default: Story = {
  args: {
    products: sampleProducts,
    onEdit: (product) => console.log('Edit product:', product),
    onDelete: (id) => console.log('Delete product:', id),
    onRefresh: () => console.log('Refresh products'),
    loading: false,
    error: null,
  },
};

export const Loading: Story = {
  args: {
    products: [],
    onEdit: (product) => console.log('Edit product:', product),
    onDelete: (id) => console.log('Delete product:', id),
    onRefresh: () => console.log('Refresh products'),
    loading: true,
    error: null,
  },
};

export const Empty: Story = {
  args: {
    products: [],
    onEdit: (product) => console.log('Edit product:', product),
    onDelete: (id) => console.log('Delete product:', id),
    onRefresh: () => console.log('Refresh products'),
    loading: false,
    error: null,
  },
};

export const WithError: Story = {
  args: {
    products: [],
    onEdit: (product) => console.log('Edit product:', product),
    onDelete: (id) => console.log('Delete product:', id),
    onRefresh: () => console.log('Refresh products'),
    loading: false,
    error: 'Failed to load products. Please check your connection and try again.',
  },
};

export const ReadOnly: Story = {
  args: {
    products: sampleProducts,
    loading: false,
    error: null,
  },
};

export const LargeDataset: Story = {
  args: {
    products: Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      name: `Product ${i + 1}`,
      description: `Description for product ${i + 1}`,
      price: Math.round((Math.random() * 500 + 10) * 100) / 100,
      stock: Math.floor(Math.random() * 100),
      category: ['Electronics', 'Clothing', 'Home & Kitchen', 'Sports', 'Accessories'][
        Math.floor(Math.random() * 5)
      ],
      isActive: Math.random() > 0.2,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    })),
    onEdit: (product) => console.log('Edit product:', product),
    onDelete: (id) => console.log('Delete product:', id),
    onRefresh: () => console.log('Refresh products'),
    loading: false,
    error: null,
  },
};
