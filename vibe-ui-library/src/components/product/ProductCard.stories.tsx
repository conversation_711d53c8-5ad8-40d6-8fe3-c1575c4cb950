import type { Meta, StoryObj } from '@storybook/react';
import { ProductCard } from './ProductCard';
import type { Product } from '../../types/Product';

const meta: Meta<typeof ProductCard> = {
  title: 'Product/ProductCard',
  component: ProductCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleProduct: Product = {
  id: 1,
  name: 'Wireless Bluetooth Headphones',
  description: 'Premium noise-cancelling wireless headphones with 30-hour battery life and superior sound quality.',
  price: 299.99,
  stock: 15,
  category: 'Electronics',
  isActive: true,
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-20T14:45:00Z',
};

const inactiveProduct: Product = {
  id: 2,
  name: 'Vintage Leather Wallet',
  description: 'Handcrafted genuine leather wallet with multiple card slots.',
  price: 89.99,
  stock: 0,
  category: 'Accessories',
  isActive: false,
  createdAt: '2024-01-10T09:15:00Z',
  updatedAt: '2024-01-10T09:15:00Z',
};

const minimalProduct: Product = {
  id: 3,
  name: 'Basic T-Shirt',
  price: 19.99,
  isActive: true,
  createdAt: '2024-01-25T16:20:00Z',
  updatedAt: '2024-01-25T16:20:00Z',
};

export const Default: Story = {
  args: {
    product: sampleProduct,
    onEdit: (product) => console.log('Edit product:', product),
    onDelete: (id) => console.log('Delete product:', id),
  },
};

export const Inactive: Story = {
  args: {
    product: inactiveProduct,
    onEdit: (product) => console.log('Edit product:', product),
    onDelete: (id) => console.log('Delete product:', id),
  },
};

export const Minimal: Story = {
  args: {
    product: minimalProduct,
    onEdit: (product) => console.log('Edit product:', product),
    onDelete: (id) => console.log('Delete product:', id),
  },
};

export const WithoutActions: Story = {
  args: {
    product: sampleProduct,
  },
};

export const ReadOnly: Story = {
  args: {
    product: sampleProduct,
    onEdit: undefined,
    onDelete: undefined,
  },
};

export const Grid: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl">
      <ProductCard
        product={sampleProduct}
        onEdit={(product) => console.log('Edit product:', product)}
        onDelete={(id) => console.log('Delete product:', id)}
      />
      <ProductCard
        product={inactiveProduct}
        onEdit={(product) => console.log('Edit product:', product)}
        onDelete={(id) => console.log('Delete product:', id)}
      />
      <ProductCard
        product={minimalProduct}
        onEdit={(product) => console.log('Edit product:', product)}
        onDelete={(id) => console.log('Delete product:', id)}
      />
    </div>
  ),
};
