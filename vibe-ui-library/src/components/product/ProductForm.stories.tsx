import type { Meta, StoryObj } from '@storybook/react';
import { ProductForm } from './ProductForm';
import type { Product } from '../../types/Product';

const meta: Meta<typeof ProductForm> = {
  title: 'Product/ProductForm',
  component: ProductForm,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleProduct: Product = {
  id: 1,
  name: 'Wireless Bluetooth Headphones',
  description: 'Premium noise-cancelling wireless headphones with 30-hour battery life and superior sound quality.',
  price: 299.99,
  stock: 15,
  category: 'Electronics',
  isActive: true,
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-20T14:45:00Z',
};

export const CreateNew: Story = {
  args: {
    product: null,
    onSubmit: (data) => console.log('Create product:', data),
    onCancel: () => console.log('Cancel create'),
    isLoading: false,
  },
};

export const EditExisting: Story = {
  args: {
    product: sampleProduct,
    onSubmit: (data) => console.log('Update product:', data),
    onCancel: () => console.log('Cancel edit'),
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    product: sampleProduct,
    onSubmit: (data) => console.log('Update product:', data),
    onCancel: () => console.log('Cancel edit'),
    isLoading: true,
  },
};

export const WithoutCancel: Story = {
  args: {
    product: null,
    onSubmit: (data) => console.log('Create product:', data),
    isLoading: false,
  },
};

export const InModal: Story = {
  render: () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4">
      <ProductForm
        product={null}
        onSubmit={(data) => console.log('Create product:', data)}
        onCancel={() => console.log('Cancel create')}
        isLoading={false}
        className="max-h-[90vh] overflow-y-auto"
      />
    </div>
  ),
};
