import * as React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "../ui/Card";
import { Button } from "../ui/Button";
import { Separator } from "../ui/Separator";
import { cn } from "../../lib/utils";
import type { Product } from "../../types/Product";

export interface ProductCardProps {
	product: Product;
	onEdit?: (product: Product) => void;
	onDelete?: (id: number) => void;
	className?: string;
}

const ProductCard = React.forwardRef<HTMLDivElement, ProductCardProps>(
	({ product, onEdit, onDelete, className }, ref) => {
		const formatPrice = (price: number) => {
			return new Intl.NumberFormat("en-US", {
				style: "currency",
				currency: "USD",
			}).format(price);
		};

		const formatDate = (dateString: string) => {
			return new Date(dateString).toLocaleDateString();
		};

		return (
			<Card
				ref={ref}
				className={cn(
					"transition-all hover:shadow-md",
					!product.isActive && "opacity-70 bg-muted/50",
					className
				)}
			>
				<CardHeader className="pb-3">
					<div className="flex items-start justify-between">
						<div className="space-y-1 flex-1">
							<h3 className="font-semibold text-lg leading-none tracking-tight">
								{product.name}
							</h3>
							{product.description && (
								<p className="text-sm text-muted-foreground">
									{product.description}
								</p>
							)}
						</div>
						<div className="flex items-center gap-2">
							<span
								className={cn(
									"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
									product.isActive
										? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
										: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
								)}
							>
								{product.isActive ? "Active" : "Inactive"}
							</span>
						</div>
					</div>
				</CardHeader>

				<CardContent className="pb-3">
					<div className="space-y-3">
						<div className="flex items-center justify-between">
							<span className="text-2xl font-bold">
								{formatPrice(product.price)}
							</span>
							{product.stock !== undefined && (
								<span className="text-sm text-muted-foreground">
									Stock: {product.stock}
								</span>
							)}
						</div>

						{product.category && (
							<div>
								<span className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10 dark:bg-blue-400/10 dark:text-blue-400 dark:ring-blue-400/30">
									{product.category}
								</span>
							</div>
						)}

						<Separator />

						<div className="space-y-1 text-xs text-muted-foreground">
							<div>Created: {formatDate(product.createdAt)}</div>
							{product.updatedAt !== product.createdAt && (
								<div>
									Updated: {formatDate(product.updatedAt)}
								</div>
							)}
						</div>
					</div>
				</CardContent>

				{(onEdit || onDelete) && (
					<CardFooter className="pt-0">
						<div className="flex gap-2 w-full">
							{onEdit && (
								<Button
									variant="outline"
									size="sm"
									onClick={() => onEdit(product)}
									className="flex-1"
								>
									Edit
								</Button>
							)}
							{onDelete && (
								<Button
									variant="destructive"
									size="sm"
									onClick={() => onDelete(product.id)}
									className="flex-1"
								>
									Delete
								</Button>
							)}
						</div>
					</CardFooter>
				)}
			</Card>
		);
	}
);

ProductCard.displayName = "ProductCard";

export { ProductCard };
