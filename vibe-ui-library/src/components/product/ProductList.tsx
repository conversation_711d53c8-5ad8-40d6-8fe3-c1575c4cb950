import * as React from "react";
import { ProductCard } from "./ProductCard";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { Label } from "../ui/Label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/Select";
import { Switch } from "../ui/Switch";
import { Separator } from "../ui/Separator";
import { cn } from "../../lib/utils";
import type { Product } from "../../types/Product";

export interface ProductListProps {
	products: Product[];
	onEdit?: (product: Product) => void;
	onDelete?: (id: number) => void;
	onRefresh?: () => void;
	loading?: boolean;
	error?: string | null;
	className?: string;
}

const ProductList = React.forwardRef<HTMLDivElement, ProductListProps>(
	(
		{
			products,
			onEdit,
			onDelete,
			onRefresh,
			loading = false,
			error,
			className,
		},
		ref
	) => {
		const [categoryFilter, setCategoryFilter] = React.useState<string>("");
		const [showActiveOnly, setShowActiveOnly] = React.useState(false);
		const [searchTerm, setSearchTerm] = React.useState("");

		// Get unique categories from products
		const categories = React.useMemo(() => {
			const uniqueCategories = Array.from(
				new Set(products.map((p) => p.category).filter(Boolean))
			);
			return uniqueCategories.sort();
		}, [products]);

		// Filter products based on search term, category, and active status
		const filteredProducts = React.useMemo(() => {
			return products.filter((product) => {
				const matchesSearch =
					!searchTerm ||
					product.name
						.toLowerCase()
						.includes(searchTerm.toLowerCase()) ||
					(product.description &&
						product.description
							.toLowerCase()
							.includes(searchTerm.toLowerCase()));

				const matchesCategory =
					!categoryFilter || product.category === categoryFilter;
				const matchesActiveFilter = !showActiveOnly || product.isActive;

				return matchesSearch && matchesCategory && matchesActiveFilter;
			});
		}, [products, searchTerm, categoryFilter, showActiveOnly]);

		const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			setSearchTerm(e.target.value);
		};

		const handleCategoryChange = (value: string) => {
			setCategoryFilter(value === "all" ? "" : value);
		};

		const handleActiveFilterChange = (checked: boolean) => {
			setShowActiveOnly(checked);
		};

		const clearFilters = () => {
			setSearchTerm("");
			setCategoryFilter("");
			setShowActiveOnly(false);
		};

		if (error) {
			return (
				<div ref={ref} className={cn("space-y-4", className)}>
					<div className="rounded-lg border border-destructive bg-destructive/10 p-4">
						<div className="flex items-center justify-between">
							<div>
								<h3 className="text-sm font-medium text-destructive">
									Error loading products
								</h3>
								<p className="text-sm text-destructive/80 mt-1">
									{error}
								</p>
							</div>
							{onRefresh && (
								<Button
									variant="outline"
									size="sm"
									onClick={onRefresh}
								>
									Try Again
								</Button>
							)}
						</div>
					</div>
				</div>
			);
		}

		return (
			<div ref={ref} className={cn("space-y-6", className)}>
				{/* Filters */}
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<h2 className="text-2xl font-bold">Products</h2>
						{onRefresh && (
							<Button
								variant="outline"
								onClick={onRefresh}
								disabled={loading}
							>
								{loading ? "Loading..." : "Refresh"}
							</Button>
						)}
					</div>

					<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
						<div className="space-y-2">
							<Label htmlFor="search">Search</Label>
							<Input
								id="search"
								placeholder="Search products..."
								value={searchTerm}
								onChange={handleSearchChange}
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="category">Category</Label>
							<Select
								value={categoryFilter || "all"}
								onValueChange={handleCategoryChange}
							>
								<SelectTrigger>
									<SelectValue placeholder="All categories" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">
										All categories
									</SelectItem>
									{categories.map((category) => (
										<SelectItem
											key={category}
											value={category!}
										>
											{category}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className="space-y-2">
							<Label htmlFor="active-filter">Filters</Label>
							<div className="flex items-center space-x-2 h-9">
								<Switch
									id="active-filter"
									checked={showActiveOnly}
									onCheckedChange={handleActiveFilterChange}
								/>
								<Label
									htmlFor="active-filter"
									className="text-sm"
								>
									Active only
								</Label>
							</div>
						</div>

						<div className="space-y-2">
							<Label>&nbsp;</Label>
							<Button
								variant="outline"
								onClick={clearFilters}
								className="w-full"
							>
								Clear Filters
							</Button>
						</div>
					</div>

					<Separator />
				</div>

				{/* Results */}
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<p className="text-sm text-muted-foreground">
							Showing {filteredProducts.length} of{" "}
							{products.length} products
						</p>
					</div>

					{loading ? (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							{Array.from({ length: 6 }).map((_, i) => (
								<div
									key={i}
									className="h-64 rounded-lg bg-muted animate-pulse"
								/>
							))}
						</div>
					) : filteredProducts.length === 0 ? (
						<div className="text-center py-12">
							<div className="space-y-2">
								<h3 className="text-lg font-medium">
									No products found
								</h3>
								<p className="text-muted-foreground">
									{products.length === 0
										? "No products have been created yet."
										: "Try adjusting your search or filter criteria."}
								</p>
								{(searchTerm ||
									categoryFilter ||
									showActiveOnly) && (
									<Button
										variant="outline"
										onClick={clearFilters}
									>
										Clear Filters
									</Button>
								)}
							</div>
						</div>
					) : (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							{filteredProducts.map((product) => (
								<ProductCard
									key={product.id}
									product={product}
									onEdit={onEdit}
									onDelete={onDelete}
								/>
							))}
						</div>
					)}
				</div>
			</div>
		);
	}
);

ProductList.displayName = "ProductList";

export { ProductList };
